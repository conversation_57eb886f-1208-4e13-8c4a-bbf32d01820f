export const GAME_CARDS = [
  {
    id: 1,
    name: '<PERSON>',
    subtitle: 'Dream Land',
    emoji: '🌟',
    rating: 4.2,
    bgGradient: 'from-accent-luxury-rose-from to-accent-luxury-rose-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-secondary-500/30',
    hoverShadow: 'hover:shadow-secondary-500/30'
  },
  {
    id: 2,
    name: 'Toad',
    subtitle: 'Kingdom Battle',
    emoji: '🍄',
    rating: 4.1,
    bgGradient: 'from-primary-600 to-primary-800',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-primary-500/30',
    hoverShadow: 'hover:shadow-primary-500/30'
  },
  {
    id: 3,
    name: '<PERSON><PERSON> <PERSON>',
    subtitle: 'Pro Gost',
    emoji: '👻',
    rating: 4.1,
    bgGradient: 'from-accent-luxury-rose-from to-accent-luxury-rose-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-error-500/40',
    hoverShadow: 'hover:shadow-error-500/40'
  },
  {
    id: 4,
    name: '<PERSON><PERSON><PERSON>',
    subtitle: 'Star Allies',
    emoji: '🦕',
    rating: 4.1,
    bgGradient: 'from-accent-sophisticated-blue-from to-accent-sophisticated-blue-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-info-500/30',
    hoverShadow: 'hover:shadow-info-500/30'
  },
  {
    id: 5,
    name: 'Wario',
    subtitle: 'Royale G',
    emoji: '👨',
    rating: 4.0,
    bgGradient: 'from-primary-600 to-primary-800',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-primary-500/30',
    hoverShadow: 'hover:shadow-primary-500/30'
  },
  {
    id: 6,
    name: 'Mario',
    subtitle: 'Odyssey',
    emoji: '🔴',
    rating: 4.8,
    bgGradient: 'from-accent-luxury-rose-from to-accent-luxury-rose-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-error-500/30',
    hoverShadow: 'hover:shadow-error-500/30'
  },
  {
    id: 7,
    name: 'Luigi',
    subtitle: 'Mansion 3',
    emoji: '💚',
    rating: 4.5,
    bgGradient: 'from-accent-premium-emerald-from to-accent-premium-emerald-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-success-500/30',
    hoverShadow: 'hover:shadow-success-500/30'
  },
  {
    id: 8,
    name: 'Yoshi',
    subtitle: 'Crafted World',
    emoji: '🥚',
    rating: 4.3,
    bgGradient: 'from-accent-refined-lime-from to-accent-refined-lime-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-success-400/30',
    hoverShadow: 'hover:shadow-success-400/30'
  },
  {
    id: 9,
    name: 'Peach',
    subtitle: 'Princess Power',
    emoji: '👑',
    rating: 4.4,
    bgGradient: 'from-accent-luxury-gold-from to-accent-luxury-gold-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-secondary-500/30',
    hoverShadow: 'hover:shadow-secondary-500/30'
  },
  {
    id: 10,
    name: 'Bowser',
    subtitle: 'Fury Mode',
    emoji: '🔥',
    rating: 4.6,
    bgGradient: 'from-accent-elegant-copper-from to-accent-elegant-copper-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-warning-500/30',
    hoverShadow: 'hover:shadow-warning-500/30'
  },
  {
    id: 11,
    name: 'Link',
    subtitle: 'Wild Adventure',
    emoji: '⚔️',
    rating: 4.9,
    bgGradient: 'from-accent-premium-teal-from to-accent-premium-teal-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-info-400/30',
    hoverShadow: 'hover:shadow-info-400/30'
  },
  {
    id: 12,
    name: 'Zelda',
    subtitle: 'Tears Kingdom',
    emoji: '✨',
    rating: 4.9,
    bgGradient: 'from-accent-luxury-indigo-from to-accent-luxury-indigo-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-primary-400/30',
    hoverShadow: 'hover:shadow-primary-400/30'
  },
  {
    id: 13,
    name: 'Samus',
    subtitle: 'Dread',
    emoji: '🤖',
    rating: 4.7,
    bgGradient: 'from-accent-refined-amber-from to-accent-luxury-rose-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-warning-500/30',
    hoverShadow: 'hover:shadow-warning-500/30'
  },
  {
    id: 14,
    name: 'Fox',
    subtitle: 'Star Command',
    emoji: '🦊',
    rating: 4.2,
    bgGradient: 'from-accent-premium-slate-from to-accent-premium-slate-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-foreground-muted/30',
    hoverShadow: 'hover:shadow-foreground-muted/30'
  },
  {
    id: 15,
    name: 'Donkey Kong',
    subtitle: 'Tropical Freeze',
    emoji: '🦍',
    rating: 4.5,
    bgGradient: 'from-accent-premium-bronze-from to-accent-premium-bronze-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-warning-600/30',
    hoverShadow: 'hover:shadow-warning-600/30'
  },
  {
    id: 16,
    name: 'Pikachu',
    subtitle: 'Electric Tale',
    emoji: '⚡',
    rating: 4.8,
    bgGradient: 'from-accent-luxury-gold-from to-accent-refined-amber-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-warning-400/30',
    hoverShadow: 'hover:shadow-warning-400/30'
  },
  {
    id: 17,
    name: 'Splatoon',
    subtitle: 'Ink Battle',
    emoji: '🎨',
    rating: 4.6,
    bgGradient: 'from-accent-elegant-cyan-from to-accent-elegant-cyan-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-info-400/30',
    hoverShadow: 'hover:shadow-info-400/30'
  },
  {
    id: 18,
    name: 'Isabelle',
    subtitle: 'Animal Crossing',
    emoji: '🏝️',
    rating: 4.7,
    bgGradient: 'from-accent-premium-emerald-from to-accent-premium-emerald-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-success-500/30',
    hoverShadow: 'hover:shadow-success-500/30'
  },
  {
    id: 19,
    name: 'Villager',
    subtitle: 'New Horizons',
    emoji: '🌳',
    rating: 4.7,
    bgGradient: 'from-accent-sophisticated-green-from to-accent-sophisticated-green-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-emerald-500/30',
    hoverShadow: 'hover:shadow-emerald-500/30'
  },
  {
    id: 20,
    name: 'Mega Man',
    subtitle: 'Legacy',
    emoji: '🤖',
    rating: 4.4,
    bgGradient: 'from-accent-sophisticated-blue-from to-accent-elegant-cyan-to',
    textColor: 'text-foreground-primary',
    shadowColor: 'shadow-blue-500/30',
    hoverShadow: 'hover:shadow-blue-500/30'
  }
]
