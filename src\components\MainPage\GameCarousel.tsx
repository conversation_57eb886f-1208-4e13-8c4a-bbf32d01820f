"use client"

import React, { useRef, useState, useEffect } from 'react'
import GameCard from './GameCard'

interface GameData {
  id: number
  name: string
  subtitle: string
  emoji: string
  rating: number
  bgGradient: string
  textColor: string
  shadowColor: string
  hoverShadow: string
}

interface GameCarouselProps {
  games: GameData[]
  searchQuery: string
}

const GameCarousel: React.FC<GameCarouselProps> = ({ games, searchQuery }) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [isDown, setIsDown] = useState(false)
  const [startX, setStartX] = useState(0)
  const [scrollLeftPos, setScrollLeftPos] = useState(0)

  // Filter games based on search query
  const filteredGames = searchQuery
    ? games.filter((game) =>
        game.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        game.subtitle.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : games

  // Scroll to center the first matched card when search changes
  useEffect(() => {
    if (searchQuery && filteredGames.length > 0 && scrollContainerRef.current) {
      // Find the index of the first matched game in the original array
      const firstMatchIndex = games.findIndex((game) => game.id === filteredGames[0].id)
      
      // Calculate the position to scroll to center the card
      const cardWidth = 256 + 24 // 256px width + 24px gap (w-64 + space-x-6)
      const containerWidth = scrollContainerRef.current.offsetWidth
      const scrollPosition = (firstMatchIndex * cardWidth) - (containerWidth / 2) + (cardWidth / 2)
      
      scrollContainerRef.current.scrollTo({
        left: Math.max(0, scrollPosition),
        behavior: 'smooth'
      })
    }
  }, [searchQuery, filteredGames, games])

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -400, behavior: 'smooth' })
    }
  }

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 400, behavior: 'smooth' })
    }
  }

  const handleMouseDown = (e: React.MouseEvent) => {
    if (!scrollContainerRef.current) return
    setIsDown(true)
    setStartX(e.pageX - scrollContainerRef.current.offsetLeft)
    setScrollLeftPos(scrollContainerRef.current.scrollLeft)
  }

  const handleMouseLeave = () => {
    setIsDown(false)
  }

  const handleMouseUp = () => {
    setIsDown(false)
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDown || !scrollContainerRef.current) return
    e.preventDefault()
    const x = e.pageX - scrollContainerRef.current.offsetLeft
    const walk = (x - startX) * 2 // Scroll speed multiplier
    scrollContainerRef.current.scrollLeft = scrollLeftPos - walk
  }

  return (
    <div className="relative w-full mx-auto">
      {filteredGames.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-xl text-foreground-tertiary">No games found matching &quot;{searchQuery}&quot;</p>
        </div>
      ) : (
        <>
          {/* Left Button - Hidden on mobile */}
          <button
            onClick={scrollLeft}
            className="hidden md:block absolute left-0 top-1/2 -translate-y-1/2 z-10 bg-background-card-hover hover:bg-background-card-active backdrop-blur-sm text-foreground-primary rounded-full p-3 transition-all duration-300 hover:scale-110 shadow-lg"
            aria-label="Scroll left"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          {/* Right Button - Hidden on mobile */}
          <button
            onClick={scrollRight}
            className="hidden md:block absolute right-0 top-1/2 -translate-y-1/2 z-10 bg-background-card-hover hover:bg-background-card-active backdrop-blur-sm text-foreground-primary rounded-full p-3 transition-all duration-300 hover:scale-110 shadow-lg"
            aria-label="Scroll right"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>

          <div 
            ref={scrollContainerRef} 
            className={`flex overflow-x-auto space-x-4 md:space-x-6 px-4 md:px-6 py-6 md:py-8 scrollbar-hide snap-x snap-mandatory ${isDown ? 'cursor-grabbing' : 'cursor-grab'} touch-pan-x`}
            onMouseDown={handleMouseDown}
            onMouseLeave={handleMouseLeave}
            onMouseUp={handleMouseUp}
            onMouseMove={handleMouseMove}
          >
            {games.map((card) => {
              const isMatch = filteredGames.some((filtered) => filtered.id === card.id)
              return (
                <div
                  key={card.id}
                  className={`transition-opacity duration-300 ${isMatch || !searchQuery ? 'opacity-100' : 'opacity-30'}`}
                >
                  <GameCard
                    name={card.name}
                    subtitle={card.subtitle}
                    emoji={card.emoji}
                    rating={card.rating}
                    bgGradient={card.bgGradient}
                    textColor={card.textColor}
                    shadowColor={card.shadowColor}
                    hoverShadow={card.hoverShadow}
                  />
                </div>
              )
            })}
          </div>
        </>
      )}
    </div>
  )
}

export default GameCarousel
