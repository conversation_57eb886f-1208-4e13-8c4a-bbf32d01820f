"use client"

import React, { useState } from 'react'
import Header from '@/components/MainPage/Header'
import HeroSection from '@/components/MainPage/HeroSection'
import GameCarousel from '@/components/MainPage/GameCarousel'
import ColorTest from '@/components/ColorTest'
import { GAME_CARDS } from '@/data/gameData'

const Page = () => {
  const [searchQuery, setSearchQuery] = useState('')

  return (
    <div className="min-h-screen bg-gradient-to-br from-bg-secondary via-bg-tertiary to-bg-primary text-text-primary">
      <Header searchQuery={searchQuery} onSearchChange={setSearchQuery} />
      <HeroSection />
      <GameCarousel games={GAME_CARDS} searchQuery={searchQuery} />
    </div>
  )
}

export default Page
